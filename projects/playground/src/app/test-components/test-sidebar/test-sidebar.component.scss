.sidebar-demo {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  position: relative;

  // Prevent any unwanted images or content from appearing
  img:not(.allowed-image) {
    display: none !important;
  }

  // Ensure clean layout
  * {
    box-sizing: border-box;
  }

  // Hero Section
  .hero-section {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
      pointer-events: none;
    }

    .hero-content {
      text-align: center;
      max-width: 800px;
      padding: 2rem;

      h1 {
        font-size: 4rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: white;
        line-height: 1.2;
      }

      p {
        font-size: 1.5rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 3rem;
        line-height: 1.6;
      }

      .scroll-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        animation: bounce 2s infinite;

        span {
          font-size: 1rem;
          color: rgba(255, 255, 255, 0.8);
        }

        ava-icon {
          font-size: 2rem;
        }
      }

      &:hover {
        cursor: pointer;
        transform: translateY(-2px);
        opacity: 0.8;
      }
    }
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  // Layout Sections
  .layout-section {
    min-height: 100vh;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;

    // Beautiful gradient backgrounds for each section
    &:nth-child(2) { // Left Sidebar + Inside Button
      background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 30% 70%, rgba(255, 154, 158, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 70% 30%, rgba(254, 207, 239, 0.3) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
      }
    }

    &:nth-child(3) { // Right Sidebar
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 20% 80%, rgba(168, 237, 234, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(254, 214, 227, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
      }
    }

    &:nth-child(4) { // Outside Button
      background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 40% 60%, rgba(255, 236, 210, 0.4) 0%, transparent 50%),
          radial-gradient(circle at 60% 40%, rgba(252, 182, 159, 0.3) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
      }
    }

    &:nth-child(5) { // Static Sidebar
      background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 25% 75%, rgba(210, 153, 194, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 75% 25%, rgba(254, 249, 215, 0.4) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
      }
    }

    &:nth-child(6) { // Grid Section
      background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 30% 70%, rgba(137, 247, 254, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 70% 30%, rgba(102, 166, 255, 0.2) 0%, transparent 50%);
        pointer-events: none;
        z-index: 0;
      }
    }

    // Ensure content is above the background
    > * {
      position: relative;
      z-index: 1;
    }

    .section-header {
      text-align: center;
      margin-bottom: 3rem;
      padding: 2rem;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
      margin-bottom: 3rem;

      h2 {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        letter-spacing: -0.02em;
      }

      p {
        font-size: 1.3rem;
        color: rgba(255, 255, 255, 0.9);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.7;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }

    .full-layout {
      max-width: 1600px;
      margin: 0 auto;
      height: calc(100vh - 200px);
      border-radius: 20px;
      overflow: hidden;
      box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.05);
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .demo-header-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem 2rem;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
        backdrop-filter: blur(10px);
        color: white;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .header-left h3 {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;
          color: white;
        }

        .header-right {
          display: flex;
          gap: 0.75rem;
        }
      }

      .layout-content {
        display: flex;
        height: calc(100% - 60px);
        overflow: hidden;
      }

      .main-content {
        flex: 1;
        padding: 2rem;
        overflow-y: auto;
        background: #fafafa;

        .content-header {
          margin-bottom: 2rem;

          h2 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
            font-weight: 600;
            color: #333;
          }

          p {
            margin: 0;
            color: #666;
            font-size: 1.125rem;
            line-height: 1.6;
          }
        }

        .content-body {
          .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 2.5rem;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
              0 20px 40px rgba(0, 0, 0, 0.1),
              0 8px 16px rgba(0, 0, 0, 0.05);

            h3 {
              margin: 0 0 1rem 0;
              font-size: 1.5rem;
              font-weight: 600;
              color: #333;
            }

            p {
              margin: 0 0 1.5rem 0;
              color: #666;
              line-height: 1.6;
              font-size: 1rem;
            }

            .stats-grid,
            .file-grid,
            .dashboard-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
              gap: 1rem;
              margin-top: 1.5rem;
            }

            .stat-item,
            .file-item,
            .dashboard-item {
              display: flex;
              align-items: center;
              gap: 1rem;
              padding: 1rem;
              background: #f8f9fa;
              border-radius: 8px;
              transition: all 0.2s ease;

              &:hover {
                background: #e9ecef;
                transform: translateY(-2px);
              }

              ava-icon {
                color: #667eea;
                font-size: 1.5rem;
              }

              .stat-info,
              .dashboard-info {
                display: flex;
                flex-direction: column;

                .stat-number,
                .dashboard-number {
                  font-size: 1.25rem;
                  font-weight: 700;
                  color: #333;
                }

                .stat-label,
                .dashboard-label {
                  font-size: 0.875rem;
                  color: #666;
                }
              }

              span {
                font-size: 0.875rem;
                font-weight: 500;
                color: #495057;
              }
            }

            .editor-placeholder {
              background: #f8f9fa;
              padding: 2rem;
              border-radius: 8px;
              border: 2px dashed #dee2e6;
              text-align: center;
              margin-top: 1.5rem;

              p {
                margin: 0.5rem 0;
                color: #6c757d;
                font-style: italic;
              }
            }
          }
        }
      }
    }
  }

  // Variants Section (Grid at the end)
  .variants-section {
    min-height: 100vh;
    padding: 4rem 2rem;
    background: #f8f9fa;

    .section-header {
      text-align: center;
      margin-bottom: 4rem;

      h2 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        font-size: 1.25rem;
        color: #666;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }
    }

    .variants-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 2rem;
      max-width: 1200px;
      margin: 0 auto;

      .variant-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .variant-header {
          padding: 1.5rem;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;

          h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: white;
          }

          p {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
          }
        }

        .variant-demo {
          padding: 1.5rem;
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 400px;
          background: #fafafa;
        }
      }
    }
  }

  // API Section
  .api-section {
    min-height: 100vh;
    padding: 4rem 2rem;
    background: white;

    .section-header {
      text-align: center;
      margin-bottom: 4rem;

      h2 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        font-size: 1.25rem;
        color: #666;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }
    }

    .api-content {
      max-width: 1200px;
      margin: 0 auto;

      .api-table {
        margin-bottom: 3rem;

        h3 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 1.5rem;
          color: #333;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          background: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

          thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

            th {
              padding: 1rem;
              text-align: left;
              font-weight: 600;
              color: white;
              font-size: 0.875rem;
            }
          }

          tbody {
            tr {
              border-bottom: 1px solid #dee2e6;

              &:last-child {
                border-bottom: none;
              }

              &:hover {
                background: #f8f9fa;
              }
            }

            td {
              padding: 1rem;
              font-size: 0.875rem;
              color: #495057;
              vertical-align: top;

              code {
                padding: 0.25rem 0.5rem;
                background: #e9ecef;
                border-radius: 3px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 0.8rem;
                color: #495057;
              }
            }
          }
        }
      }
    }
  }

  // Demo content styles
  .demo-header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    height: 60px; // Fixed header height

    // Extend background to cover entire row including hover area (C = A + B)
    &::before {
      content: '';
      position: absolute;
      left: -1.5rem;
      right: -50px; // Extend into hover area (B area) for left-positioned
      top: 0;
      bottom: 0;
      background: transparent;
      z-index: -1;
    }

    // For right-positioned sidebar, extend to the left
    .ava-sidebar.right-positioned & {
      &::before {
        left: -50px; // Extend into hover area (B area) for right-positioned
        right: -1.5rem;
      }
    }

    .header-title {
      font-weight: 600;
      font-size: 1.1rem;
      color: #2d3748;
      text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
      position: relative;
      z-index: 1;
    }

    ava-icon {
      color: #667eea;
      filter: drop-shadow(0 1px 2px rgba(102, 126, 234, 0.3));
      position: relative;
      z-index: 1;
    }
  }

  .demo-content {
    padding: 1rem;

    .nav-item {
      display: flex;
      align-items: center;
      gap: 0.875rem;
      padding: 0.875rem 1rem;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      margin-bottom: 0.5rem;
      position: relative;
      background: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateX(6px) scale(1.02);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        border-color: rgba(102, 126, 234, 0.4);
      }

      &.active {
        background: rgba(102, 126, 234, 0.1);
        border-left: 4px solid #667eea;
        padding-left: calc(1rem - 4px);

        span {
          color: #667eea;
          font-weight: 600;
        }

        ava-icon {
          color: #667eea;
        }
      }

      span {
        font-size: 0.95rem;
        color: #2d3748;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      ava-icon {
        color: #718096;
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        transition: color 0.3s ease;
      }
    }

    // Collapsed state styles - minimal like Figma
    :host ::ng-deep .ava-sidebar.collapsed .demo-content .nav-item {
      justify-content: center;
      padding: 0.75rem;
      margin: 0.5rem auto;
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: transparent;
      border: none;

      span {
        display: none;
      }

      &.active {
        border-left: none;
        padding: 0.75rem;
        background: rgba(102, 126, 234, 0.1);

        &::before {
          display: none;
        }
      }
    }




  }

  .demo-footer-content {
    padding: 1rem 1.5rem;
    background: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.1);

    small {
      color: #718096;
      font-size: 0.8rem;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      justify-content: center;
      padding: 0.75rem 1rem;
      background: transparent;
      border-radius: 12px;
      border: 1px solid rgba(0, 0, 0, 0.1);

      span {
        font-size: 0.9rem;
        font-weight: 500;
        color: #2d3748;
      }

      ava-icon {
        color: #667eea;
      }
    }

    .storage-info {
      padding: 0.5rem;

      small {
        display: block;
        margin-bottom: 0.5rem;
        color: #6c757d;
        font-size: 0.75rem;
      }

      .storage-bar {
        width: 100%;
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        overflow: hidden;

        .storage-used {
          height: 100%;
          background: linear-gradient(90deg, #28a745, #20c997);
          transition: width 0.3s ease;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 1400px) {
    .layout-section .full-layout {
      margin: 0 1rem;
    }
  }

  @media (max-width: 1200px) {
    .variants-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .hero-content h1 {
      font-size: 3rem;
    }

    .section-header h2 {
      font-size: 2.5rem;
    }

    .layout-section {
      .full-layout {
        height: calc(100vh - 150px);

        .main-content {
          padding: 1.5rem;

          .content-header h2 {
            font-size: 1.75rem;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .hero-section {
      padding: 2rem 1rem;

      .hero-content {
        h1 {
          font-size: 2.5rem;
        }

        p {
          font-size: 1.25rem;
        }
      }
    }

    .layout-section,
    .variants-section,
    .api-section {
      padding: 3rem 1rem;

      .section-header h2 {
        font-size: 2rem;
      }

      .full-layout {
        height: calc(100vh - 120px);

        .demo-header-bar {
          padding: 0.75rem 1rem;

          .header-left h3 {
            font-size: 1.25rem;
          }

          .header-right {
            gap: 0.5rem;
          }
        }

        .main-content {
          padding: 1rem;

          .content-header {
            h2 {
              font-size: 1.5rem;
            }

            p {
              font-size: 1rem;
            }
          }

          .content-body .content-card {
            padding: 1.5rem;

            .stats-grid,
            .file-grid,
            .dashboard-grid {
              grid-template-columns: 1fr;
            }
          }
        }
      }
    }

    .variant-item {
      .variant-demo {
        min-height: 350px;
      }

      .variant-header {
        padding: 1rem;
      }
    }

    .api-content {
      .api-table table {
        font-size: 0.75rem;

        th, td {
          padding: 0.75rem;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .hero-content h1 {
      font-size: 2rem;
    }

    .section-header h2 {
      font-size: 1.75rem;
    }

    .layout-section .full-layout {
      height: calc(100vh - 100px);

      .main-content .content-body .content-card {
        padding: 1rem;

        h3 {
          font-size: 1.25rem;
        }
      }
    }

    .variant-item .variant-demo {
      min-height: 300px;
    }
  }
}

// Sidebar container styles - implements C = A + B concept (only for outside button variants)
:host ::ng-deep .ava-sidebar-container.outside-button {
  position: relative;
  height: 100vh;
  display: flex;

  // For left-positioned sidebar, hover area goes to the right
  &:not(.right-positioned) {
    flex-direction: row;
  }

  // For right-positioned sidebar, hover area goes to the left
  &.right-positioned {
    flex-direction: row-reverse;
  }
}

// Ensure main content doesn't overlap with hover area for outside button variants
.layout-content:has(.ava-sidebar-container.outside-button) .main-content {
  margin-left: 40px; // Account for hover area width
}

.layout-content:has(.ava-sidebar-container.outside-button.right-positioned) .main-content {
  margin-left: 0;
  margin-right: 40px; // Account for hover area width on right side
}
