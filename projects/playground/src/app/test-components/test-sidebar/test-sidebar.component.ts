import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from '../../../../../play-comp-library/src/lib/components/sidebar/sidebar.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'test-sidebar',
  standalone: true,
  imports: [CommonModule, SidebarComponent, ButtonComponent, IconComponent],
  templateUrl: './test-sidebar.component.html',
  styleUrl: './test-sidebar.component.scss',
})
export class TestSidebarComponent {
  // Demo state variables
  leftSidebarCollapsed = false;
  rightSidebarCollapsed = false;
  basicSidebarCollapsed = false;
  customSizeCollapsed = false;
  outsideButtonCollapsed = false;
  staticSidebarCollapsed = false;

  // Demo configurations
  demoConfigs = {
    basic: {
      width: '260px',
      collapsedWidth: '70px',
      height: '600px',
      showCollapseButton: true,
      buttonVariant: 'inside' as 'inside' | 'outside',
      showHeader: true,
      showFooter: true,
      position: 'left' as 'left' | 'right'
    },
    leftPositioned: {
      width: '280px',
      collapsedWidth: '70px',
      height: '600px',
      showCollapseButton: true,
      buttonVariant: 'inside' as 'inside' | 'outside',
      showHeader: true,
      showFooter: true,
      position: 'left' as 'left' | 'right'
    },
    rightPositioned: {
      width: '280px',
      collapsedWidth: '70px',
      height: '600px',
      showCollapseButton: true,
      buttonVariant: 'inside' as 'inside' | 'outside',
      showHeader: true,
      showFooter: true,
      position: 'right' as 'left' | 'right'
    },
    customSize: {
      width: '320px',
      collapsedWidth: '80px',
      height: '600px',
      showCollapseButton: true,
      buttonVariant: 'outside' as 'inside' | 'outside',
      showHeader: true,
      showFooter: true,
      position: 'left' as 'left' | 'right'
    },
    outsideButton: {
      width: '300px',
      collapsedWidth: '80px',
      height: '600px',
      showCollapseButton: true,
      buttonVariant: 'outside' as 'inside' | 'outside',
      showHeader: true,
      showFooter: true,
      position: 'left' as 'left' | 'right'
    },
    staticSidebar: {
      width: '280px',
      height: '600px',
      showCollapseButton: false,
      showHeader: true,
      showFooter: true,
      position: 'left' as 'left' | 'right'
    }
  };

  onLeftSidebarToggle(collapsed: boolean): void {
    this.leftSidebarCollapsed = collapsed;
  }

  onRightSidebarToggle(collapsed: boolean): void {
    this.rightSidebarCollapsed = collapsed;
  }

  onBasicSidebarToggle(collapsed: boolean): void {
    this.basicSidebarCollapsed = collapsed;
  }

  onCustomSizeToggle(collapsed: boolean): void {
    this.customSizeCollapsed = collapsed;
  }

  onOutsideButtonToggle(collapsed: boolean): void {
    this.outsideButtonCollapsed = collapsed;
  }

  onStaticSidebarToggle(collapsed: boolean): void {
    this.staticSidebarCollapsed = collapsed;
  }

  scrollToFirstDemo(): void {
    const firstSection = document.querySelector('.layout-section');
    if (firstSection) {
      firstSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }
}
