<div class="ava-sidebar-container"
     [class.right-positioned]="isRightPositioned"
     [class.outside-button]="buttonVariant === 'outside'"
     [style.--sidebar-width]="sidebarWidth"
     [style.--hover-area-width]="hoverAreaWidth">

  <div class="ava-sidebar"
       [style.width]="sidebarWidth"
       [style.height]="height"
       [class.collapsed]="collapsed"
       [class.right-positioned]="isRightPositioned">
    
    <!-- Header Section -->
    <div class="sidebar-header" *ngIf="showHeader">
      <!-- Logo Section -->
      <div class="logo-section" [class.collapsed]="collapsed">
        <img *ngIf="logoSrc" [src]="logoSrc" [alt]="'Logo'" class="logo-image" />
        <ng-content select="[slot=header]"></ng-content>
      </div>

      <!-- Search Section -->
      <div class="search-section" *ngIf="showSearch && !collapsed">
        <div class="search-container">
          <ava-icon name="search" size="small"></ava-icon>
          <input type="text" [placeholder]="searchPlaceholder" class="search-input" />
        </div>
      </div>

      <!-- Inside Button Variant -->
      <div class="header-controls"
           *ngIf="showCollapseButton && buttonVariant === 'inside'">
        <ava-button [iconName]="collapseButtonIcon"
                  iconPosition="only"
                  size="small"
                  (click)="toggleCollapse()"
                  variant="primary">
        </ava-button>
      </div>
    </div>

    <!-- Navigation Content Section -->
    <div class="sidebar-content">
      <ng-container *ngIf="!collapsed">
        <ng-content select="[slot=navigation]"></ng-content>
      </ng-container>
      <ng-container *ngIf="collapsed">
        <ng-content select="[slot=collapsed-navigation]"></ng-content>
      </ng-container>
    </div>

    <!-- Footer with Profile Section -->
    <div class="sidebar-footer" *ngIf="showFooter">
      <div class="profile-section" [class.collapsed]="collapsed">
        <ng-content select="[slot=footer]"></ng-content>
      </div>
    </div>
  </div>

  <!-- Outside Button Variant - Hover Area -->
  <div class="hover-area"
       [class.right-positioned]="isRightPositioned"
       [class.left-positioned]="!isRightPositioned"
       *ngIf="showCollapseButton && buttonVariant === 'outside'">
    <div class="hover-area-content">
      <ava-button variant="primary"
                [iconName]="collapseButtonIcon"
                iconPosition="only"
                size="small"
                (click)="toggleCollapse()">
      </ava-button>
    </div>
  </div>
</div>
