$sidebar-border: var(--sidebar-border);
$sidebar-background: var(--sidebar-background, #ffffff);
$search-background: var(--search-background, #f5f5f5);
$text-color: var(--text-color, #333333);

.ava-sidebar-container {
  display: flex;
  position: relative;
  width: fit-content;

  &.right-positioned {
    flex-direction: row-reverse;
  }
}

.ava-sidebar {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  border: $sidebar-border;
  background: $sidebar-background;

  .sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .logo-section {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;

      &.collapsed {
        justify-content: center;
        margin-bottom: 0;
      }

      .logo-image {
        height: 40px;
        width: auto;
        object-fit: contain;
      }
    }

    .search-section {
      margin-top: 1rem;
      
      .search-container {
        display: flex;
        align-items: center;
        background: $search-background;
        border-radius: 4px;
        padding: 0.5rem;
        gap: 0.5rem;

        .search-input {
          border: none;
          background: none;
          outline: none;
          width: 100%;
          color: $text-color;
          font-size: 0.875rem;

          &::placeholder {
            color: rgba(0, 0, 0, 0.5);
          }
        }
      }
    }
  }

  &.collapsed {
    align-items: center;

    .logo-section {
      padding: 0.5rem;
      
      .logo-image {
        height: 32px;
      }
    }

    .header-content,
    .sidebar-footer {
      display: none;
    }

    .sidebar-header {
      justify-content: center;
      align-items: center;
      padding-top: 8px;
    }

    .sidebar-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0.5rem;
    }
  }

  &.right-positioned {
    border-left: $sidebar-border;
    border-right: none;
  }
}

.header-controls {
  display: flex;
  align-items: center;
  justify-content: center;

 }

.sidebar-content {
  flex: 1 1 auto;
  padding: 1rem;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
}

// Only show hover area for outside button variants
.ava-sidebar-container.outside-button .hover-area {
  position: absolute;
  top: 0;
  height: 100%;
  width: var(--hover-area-width, 50px); // Use dynamic hover area width
  z-index: 10;
  display: flex;
  align-items: flex-start; // Align button to top
  justify-content: center;
  padding-top: 8px; // Position button at top with small padding
  transition: all 0.3s ease;
  opacity: 0;
  pointer-events: auto; // Always allow hover area to be interactive

  .hover-area-content {
    display: flex;
    width: 100%; // Take full width of hover area
    height: 100%; // Take full height of hover area
    opacity: 0;
    transition: all 0.3s ease;
  }

  // Position the hover area in the white space next to the sidebar
  &.right-positioned {
    right: var(--sidebar-width, 260px); // Position in the white space to the left of sidebar
  }

  &.left-positioned {
    left: var(--sidebar-width, 260px); // Position in the white space to the right of sidebar
  }

  &:hover .hover-area-content {
    opacity: 1;
  }
}

// Footer styling
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);

  .profile-section {
    display: flex;
    align-items: center;
    gap: 1rem;

    &.collapsed {
      justify-content: center;
    }
  }
}
.ava-sidebar-container.outside-button:hover .hover-area,
.ava-sidebar-container.outside-button .hover-area:hover {
  opacity: 1;
  pointer-events: auto;

  .hover-area-content {
    opacity: 1;
  }
}

// Handle collapsed state positioning
.ava-sidebar-container.outside-button.collapsed .hover-area {
  &.left-positioned {
    left: var(--collapsed-width, 80px); // Position after collapsed sidebar
  }

  &.right-positioned {
    right: var(--collapsed-width, 80px); // Position after collapsed sidebar
  }
}

.sidebar-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ava-sidebar.collapsed .sidebar-header{
  justify-content: center;
}

// Collapsed state - minimal like Figma
.ava-sidebar.collapsed {
  .sidebar-content {
    padding: 0.5rem;
    align-items: center;

    .nav-item {
      justify-content: center;
      width: 40px;
      height: 40px;
      margin: 0.25rem auto;

      .nav-text {
        display: none;
      }
    }

    // Hide text in all collapsed items
    .demo-content,
    .user-info {
      justify-content: center;

      .nav-item span,
      span {
        display: none;
      }
    }
  }

  // Center header content in collapsed state
  .sidebar-header {
    .demo-header-content {
      justify-content: center;

      .header-title,
      span {
        display: none;
      }
    }
  }
}

